#!/usr/bin/env python3
"""
OFE2E 重构版启动脚本
展示重构后的GUI配置逻辑
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """主函数"""
    print("🚀 启动OFE2E重构版...")
    print("✨ 重构亮点:")
    print("  • 移除了独立的列配置模块")
    print("  • 数据源配置集成列配置功能")
    print("  • 映射源配置简化为文本输入")
    print("  • 支持新格式：原列名[新列名]")
    print("  • 实时格式验证和提示")
    print()
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from gui.main_window import MainWindow
        
        # 启用高DPI支持
        if hasattr(Qt, 'AA_EnableHighDpiScaling'):
            QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
            QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("OFE2E 重构版")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("OFE2E Team")
        
        # 创建主窗口
        window = MainWindow()
        
        # 添加欢迎信息
        window.log_message("🎉 欢迎使用OFE2E重构版!")
        window.log_message("✨ 重构亮点:")
        window.log_message("  • 移除了独立的列配置模块")
        window.log_message("  • 数据源配置集成列配置功能")
        window.log_message("  • 映射源配置简化为文本输入")
        window.log_message("  • 支持新格式：原列名[新列名]")
        window.log_message("  • 实时格式验证和提示")
        window.log_message("")
        window.log_message("💡 使用示例:")
        window.log_message("  • 数据列：产品名称[标准产品名]")
        window.log_message("  • 数据主键列：产品代码")
        window.log_message("  • 映射主键列：序号[云序号]")
        window.log_message("  • 工作表：映射表")
        
        # 设置一些示例配置
        window.data_column_edit.setText("产品名称[标准产品名]")
        window.data_key_column_edit.setText("产品代码")
        window.mapping_key_edit.setText("序号[云序号]")
        window.mapping_sheet_edit.setText("映射表")
        
        # 显示窗口
        window.show()
        
        print("✅ GUI界面已启动")
        print("📖 请查看界面中的配置示例和实时验证")
        print("🔧 测试新的配置格式和验证功能")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所有依赖包:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
