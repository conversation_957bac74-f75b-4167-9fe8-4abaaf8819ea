"""
应用程序设置和常量定义
"""

import os

# 应用程序信息
APP_NAME = "OFE2E"
APP_VERSION = "1.0.0"
APP_AUTHOR = "OFE2E Team"

# 文件路径
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
CONFIG_DIR = os.path.join(BASE_DIR, "config")
RESOURCES_DIR = os.path.join(BASE_DIR, "resources")

# 确保目录存在
os.makedirs(CONFIG_DIR, exist_ok=True)
os.makedirs(RESOURCES_DIR, exist_ok=True)

# 配置文件
DEFAULT_CONFIG_FILE = os.path.join(CONFIG_DIR, "app_config.json")

# 支持的文件格式
SUPPORTED_EXCEL_FORMATS = [
    "*.xlsx",
    "*.xls"
]

# 默认配置（重构后）
DEFAULT_CONFIG = {
    "data_files": [],
    "mapping_file": "",
    "mapping_sheet": "Sheet1",
    "mapping_key_column": "",
    "data_column": "",
    "data_key_column": "",
    "output_directory": "",
    "include_header": True,
    "auto_open_file": True,
    "window_geometry": {
        "x": 100,
        "y": 100,
        "width": 1200,
        "height": 750
    }
}

# 日志设置
LOG_FORMAT = "[{timestamp}] {level}: {message}"
LOG_DATE_FORMAT = "%H:%M:%S"

# UI设置
UI_COLORS = {
    "primary": "#4a90e2",
    "primary_dark": "#357abd",
    "success": "#28a745",
    "success_dark": "#218838",
    "danger": "#dc3545",
    "danger_dark": "#c82333",
    "secondary": "#6c757d",
    "secondary_dark": "#5a6268",
    "light": "#f8f9fa",
    "dark": "#343a40"
}
