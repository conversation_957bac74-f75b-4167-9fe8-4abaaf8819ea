# OFE2E - Excel数据映射工具

## 项目介绍
OFE2E是一个基于Python的Excel数据映射工具，支持多数据源处理、键值映射转换和结果输出。

## 功能特性
- 🔄 支持多Excel文件作为数据源
- 🗝️ 灵活的键值映射配置
- 🎨 美观的GUI界面
- ⚙️ 配置保存和加载
- 📊 实时处理进度显示

## 技术架构
- **GUI框架**: PyQt5/PySide2
- **数据处理**: pandas + openpyxl
- **配置管理**: JSON
- **界面样式**: QSS样式表

## 安装说明

### 环境要求
- Python 3.7+
- Windows/macOS/Linux

### 安装步骤
```bash
# 1. 克隆项目
git clone <repository-url>
cd ofe2e

# 2. 安装依赖
pip install -r requirements.txt

# 3. 运行程序
python main.py
```

## 使用说明

### 基本流程
1. **选择数据源**: 选择一个或多个Excel文件作为数据源
2. **配置映射源**: 指定键值映射文件
3. **设置参数**: 配置数据列、主键列和输出列名
4. **执行转换**: 点击开始处理，生成结果文件

### 界面预览
程序提供了美观的GUI界面，包含以下主要区域：
- **左侧配置面板**: 数据源、映射源、列配置、输出设置
- **右侧信息面板**: 数据预览和处理日志
- **底部操作区**: 进度显示、配置管理、处理控制

### 快速开始
```bash
# 测试GUI界面
python test_gui.py

# 运行完整程序
python main.py
```

## 开发状态

### ✅ 已完成 (第一阶段)
- 完整的GUI界面设计和实现
- 配置管理系统
- 项目架构搭建
- 基础交互功能

### 🔄 开发中 (第二阶段)
- Excel文件处理模块
- 数据映射引擎
- 业务逻辑集成

### ⏳ 计划中 (第三阶段)
- 测试模块开发
- 性能优化
- 用户体验改进

## 项目结构
```
ofe2e/
├── src/                    # 源代码
│   ├── gui/               # GUI界面模块
│   ├── config/            # 配置管理
│   ├── core/              # 核心业务逻辑 (待开发)
│   └── utils/             # 工具函数 (待开发)
├── docs/                  # 项目文档
├── resources/             # 资源文件
├── config/                # 配置文件存储
├── main.py               # 程序入口
├── test_gui.py           # GUI测试
└── requirements.txt      # 依赖包
```

## 贡献指南
欢迎提交Issue和Pull Request来改进项目。

## 许可证
本项目采用开源许可证，具体请查看LICENSE文件。

#### 参与贡献

1.  Fork 本仓库
2.  新建 Feat_xxx 分支
3.  提交代码
4.  新建 Pull Request


#### 特技

1.  使用 Readme\_XXX.md 来支持不同的语言，例如 Readme\_en.md, Readme\_zh.md
2.  Gitee 官方博客 [blog.gitee.com](https://blog.gitee.com)
3.  你可以 [https://gitee.com/explore](https://gitee.com/explore) 这个地址来了解 Gitee 上的优秀开源项目
4.  [GVP](https://gitee.com/gvp) 全称是 Gitee 最有价值开源项目，是综合评定出的优秀开源项目
5.  Gitee 官方提供的使用手册 [https://gitee.com/help](https://gitee.com/help)
6.  Gitee 封面人物是一档用来展示 Gitee 会员风采的栏目 [https://gitee.com/gitee-stars/](https://gitee.com/gitee-stars/)
